﻿<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          xmlns:tools="http://schemas.android.com/tools"
          package="com.bytedance.android">
  <!-- 必要权限 -->
  <uses-permission android:name="android.permission.INTERNET" />
  <!-- 可选权限 -->
  <uses-permission android:name="android.permission.READ_PHONE_STATE" />
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
  <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
  <uses-permission android:name="android.permission.VIBRATE" />
  <!--suppress DeprecatedClassUsageInspection -->
  <uses-permission android:name="android.permission.GET_TASKS" />
  <!-- 如果有视频相关的广告且使用textureView播放，请务必添加，否则黑屏 -->
  <uses-permission android:name="android.permission.WAKE_LOCK" />
  <!--可选，穿山甲提供“获取地理位置权限”和“不给予地理位置权限，开发者传入地理位置参数”两种方式上报用户位置，两种方式均可不选，添加位置权限或参数将帮助投放定位广告-->
  <!--请注意：无论通过何种方式提供给穿山甲用户地理位置，均需向用户声明地理位置权限将应用于穿山甲广告投放，穿山甲不强制获取地理位置信息-->
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
  <!--demo场景用到的权限，不是必须的-->
  <uses-permission android:name="android.permission.RECEIVE_USER_PRESENT" />
  <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
  <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" />
  <!-- 建议添加“query_all_package”权限，穿山甲将通过此权限在Android R系统上判定广告对应的应用是否在用户的app上安装，避免投放错误的广告，以此提高用户的广告体验。若添加此权限，需要在您的用户隐私文档中声明！ -->
  <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
  <application
          android:hardwareAccelerated="true"
          android:networkSecurityConfig="@xml/network_config"
          android:requestLegacyExternalStorage="true"
          android:supportsRtl="true">
    <meta-data
            android:name="test"
            android:value="one" />
    <meta-data
            android:name="channel"
            android:value="two" />
    <meta-data
            android:name="Channel_app"
            android:value="three" />

    <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

    <!-- csj start -->
    <provider
            android:name="com.bytedance.sdk.openadsdk.TTFileProvider"
            android:authorities="${applicationId}.TTFileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
      <meta-data
              android:name="android.support.FILE_PROVIDER_PATHS"
              android:resource="@xml/file_paths" />
    </provider>
    <provider
            android:name="com.bytedance.sdk.openadsdk.multipro.TTMultiProvider"
            android:authorities="${applicationId}.TTMultiProvider"
            android:exported="false" />
    <!-- csj end -->

    <!-- GDT start -->
    <!-- targetSDKVersion >= 24时才需要添加这个provider。provider的authorities属性的值为${applicationId}.fileprovider，
    请开发者根据自己的${applicationId}来设置这个值，例如本例中applicationId为"com.qq.e.union.demo"。 -->
    <provider
        android:name="com.qq.e.comm.GDTFileProvider"
        android:authorities="${applicationId}.gdt.fileprovider"
        android:exported="false"
        android:grantUriPermissions="true">
      <meta-data
          android:name="android.support.FILE_PROVIDER_PATHS"
          android:resource="@xml/gdt_file_path" />
    </provider>
    <!-- GDT end================== -->

    <!-- sigmob start================== -->
    <provider
        android:name="com.sigmob.sdk.SigmobFileProvider"
        android:authorities="${applicationId}.sigprovider"
        android:exported="false"
        android:grantUriPermissions="true">
      <meta-data
          android:name="android.support.FILE_PROVIDER_PATHS"
          android:resource="@xml/sigmob_provider_paths" />
    </provider>
    <!-- sigmob end -->

    <!-- baidu start -->
    <!-- 如果targetSdkVersion设置值>=24，则强烈建议添加以下provider，否则会影响app变现 -->
    <!-- android:authorities="${packageName}.bd.provider" authorities中${packageName}部分必须替换成app自己的包名 -->
    <!-- 原来的FileProvider在新版本中改为BdFileProvider,继承自v4的FileProvider,需要在应用内引用support-v4包 -->
    <provider
        android:name="com.baidu.mobads.sdk.api.BdFileProvider"
        android:authorities="${applicationId}.bd.provider"
        android:exported="false"
        android:grantUriPermissions="true">
      <meta-data
          android:name="android.support.FILE_PROVIDER_PATHS"
          android:resource="@xml/bd_file_paths" />
    </provider>
    <!-- baidu end -->

    <!-- klevin start -->
    <provider
        android:name="com.tencent.klevin.utils.FileProvider"
        android:authorities="${applicationId}.klevin.fileProvider"
        android:exported="false"
        android:grantUriPermissions="true">
      <meta-data
          android:name="android.support.FILE_PROVIDER_PATHS"
          android:resource="@xml/klevin_provider_paths" />
    </provider>
    <!-- klevin end -->

    <!-- mintegral start -->
    <provider
        android:name="com.mbridge.msdk.foundation.tools.MBFileProvider"
        android:authorities="${applicationId}.mbFileProvider"
        android:exported="false"
        android:grantUriPermissions="true">
      <meta-data
          android:name="android.support.FILE_PROVIDER_PATHS"
          android:resource="@xml/mb_provider_paths" />
    </provider>
    <!-- mintegral end -->

  </application>
</manifest>
<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#fff"
        tools:ignore="HardcodedText">
        <!-- icon+广告源+关闭按钮 layout -->

        <include
            android:id="@+id/icon_source_layout"
            layout="@layout/listitem_ad_icon_source_layout"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="5dp"
            android:layout_marginRight="10dp"
            android:paddingEnd="10dp"
            android:paddingRight="10dp" />

        <TextView
            android:id="@+id/tv_listitem_ad_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/icon_source_layout"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="5dp"
            android:layout_marginRight="10dp"
            android:ellipsize="end"
            android:lineSpacingMultiplier="1"
            android:maxLines="2"
            android:singleLine="false"
            android:text="劳力士服务中心，清洗保养，更换配件，9秒费用查询"
            android:textColor="@android:color/black"
            android:textSize="18sp" />

        <!-- -->
        <LinearLayout
            android:id="@+id/layout_image_group"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_listitem_ad_desc"
            android:layout_centerHorizontal="true"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_listitem_image1"
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_marginEnd="5dp"
                android:layout_marginRight="5dp"
                android:layout_weight="1"
                android:scaleType="centerCrop" />

            <ImageView
                android:id="@+id/iv_listitem_image2"
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_marginEnd="5dp"
                android:layout_marginRight="5dp"
                android:layout_weight="1"
                android:scaleType="centerCrop" />

            <ImageView
                android:id="@+id/iv_listitem_image3"
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:layout_weight="1"
                android:scaleType="centerCrop" />
        </LinearLayout>


        <!-- title+creativeBtn layout -->
        <include
            android:id="@+id/ad_title_creative_btn_layout"
            layout="@layout/listitem_ad_title_creative_btn_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/layout_image_group"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="4dp"
            android:layout_marginRight="10dp" />

        <!--==== 测试下载状态控制功能 begin ========-->
        <include
            layout="@layout/listitem_ad_download_btn_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/ad_title_creative_btn_layout"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:gravity="center"
            android:orientation="horizontal" />
        <!--==== 测试下载状态控制功能 end ========-->

    </RelativeLayout>
</FrameLayout>
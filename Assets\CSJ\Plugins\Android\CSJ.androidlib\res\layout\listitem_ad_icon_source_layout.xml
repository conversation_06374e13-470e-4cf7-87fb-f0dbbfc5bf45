<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/icon_source_layout"
    android:layout_width="match_parent"
    android:layout_height="30dp"
    android:layout_marginLeft="10dp"
    android:layout_marginRight="10dp"
    android:layout_marginTop="5dp"
    android:paddingEnd="10dp"
    android:paddingRight="10dp">

    <ImageView
        android:id="@+id/iv_listitem_icon"
        android:layout_width="30dp"
        android:layout_height="match_parent"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="10dp"
        android:layout_marginRight="10dp" />

    <TextView
        android:id="@+id/tv_listitem_ad_source"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerVertical="true"
        android:layout_toEndOf="@+id/iv_listitem_icon"
        android:layout_toLeftOf="@+id/iv_listitem_dislike_layout"
        android:layout_toRightOf="@+id/iv_listitem_icon"
        android:layout_toStartOf="@+id/iv_listitem_dislike_layout"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:singleLine="true"
        android:text="着陆无双"
        android:textColor="#70000000"
        android:textSize="16sp" />


    <FrameLayout
        android:id="@+id/iv_listitem_dislike_layout"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <ImageView
            android:id="@+id/iv_listitem_dislike"
            android:layout_width="20dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="10dp"
            android:layout_marginStart="10dp"
            android:visibility="gone"
            android:clickable="true"
            android:focusable="true"
            android:src="@drawable/dislike_icon" />
    </FrameLayout>


</RelativeLayout>
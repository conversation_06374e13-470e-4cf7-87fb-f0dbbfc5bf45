<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#fff"
        tools:ignore="HardcodedText">

        <include
            android:id="@+id/icon_source_layout"
            layout="@layout/listitem_ad_icon_source_layout" />


        <TextView
            android:id="@+id/tv_listitem_ad_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/icon_source_layout"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="3dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="3dp"
            android:ellipsize="end"
            android:lineSpacingMultiplier="1"
            android:maxLines="2"
            android:singleLine="false"
            android:text="劳力士服务中心，清洗保养，更换配件，9秒费用查询"
            android:textColor="@android:color/black"
            android:textSize="18sp" />


        <FrameLayout
            android:id="@+id/iv_listitem_video"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:layout_below="@id/tv_listitem_ad_desc"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:background="@android:color/background_dark"
            android:scaleType="centerCrop" />


        <!-- title+creativeBtn layout -->
        <include
            android:id="@+id/ad_title_creative_btn_layout"
            layout="@layout/listitem_ad_title_creative_btn_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/iv_listitem_video"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp" />


    </RelativeLayout>
</FrameLayout>
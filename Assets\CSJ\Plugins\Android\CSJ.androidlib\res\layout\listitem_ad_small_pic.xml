<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#fff"
        tools:ignore="HardcodedText">

        <RelativeLayout
            android:id="@+id/ad_contentPanel"
            android:layout_width="match_parent"
            android:layout_height="85dp"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="10dp">


            <ImageView
                android:id="@+id/iv_listitem_image"
                android:layout_width="100dp"
                android:layout_height="match_parent"
                android:layout_alignParentEnd="true"
                android:layout_alignParentRight="true"
                android:background="@drawable/tt_ad_cover_btn_begin_bg"
                android:scaleType="centerCrop" />


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerVertical="true"
                android:layout_toStartOf="@+id/iv_listitem_image"
                android:layout_toLeftOf="@+id/iv_listitem_image"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_listitem_ad_desc"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:lineSpacingMultiplier="1.1"
                    android:maxLines="2"
                    android:text="80后的回忆！经典三国完美复刻，安卓用户的福利"
                    android:textColor="@android:color/black"
                    android:textSize="18sp" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:layout_marginTop="3dp"
                    android:paddingEnd="10dp"
                    android:paddingRight="10dp">

                    <ImageView
                        android:id="@+id/iv_listitem_icon"
                        android:layout_width="30dp"
                        android:layout_height="match_parent"
                        android:layout_alignParentStart="true"
                        android:layout_alignParentLeft="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="10dp"
                        android:layout_marginRight="10dp"
                        android:src="@drawable/tt_mute" />

                    <TextView
                        android:id="@+id/tv_listitem_ad_source"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_centerVertical="true"
                        android:layout_toStartOf="@+id/iv_listitem_dislike"
                        android:layout_toLeftOf="@+id/iv_listitem_dislike"
                        android:layout_toEndOf="@+id/iv_listitem_icon"
                        android:layout_toRightOf="@+id/iv_listitem_icon"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:singleLine="true"
                        android:text="着陆无双"
                        android:textColor="#70000000"
                        android:textSize="16sp" />

                    <ImageView
                        android:id="@+id/iv_listitem_dislike"
                        android:layout_width="20dp"
                        android:layout_height="match_parent"
                        android:layout_alignParentEnd="true"
                        android:layout_alignParentRight="true"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginLeft="10dp"
                        android:src="@drawable/dislike_icon"
                        android:clickable="true"
                        android:focusable="true" />
                </RelativeLayout>

            </LinearLayout>
        </RelativeLayout>

        <!-- title+creativeBtn layout -->
        <include
            android:id="@+id/ad_title_creative_btn_layout"
            layout="@layout/listitem_ad_title_creative_btn_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/ad_contentPanel"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp" />

    </RelativeLayout>
</FrameLayout>
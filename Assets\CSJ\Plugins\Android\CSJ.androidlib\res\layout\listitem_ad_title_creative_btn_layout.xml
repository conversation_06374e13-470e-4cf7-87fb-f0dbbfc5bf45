<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ad_title_creative_btn_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="10dp"
    android:layout_marginRight="10dp"
    android:layout_marginBottom="2dp"
    android:background="#F4F5F7"
    android:paddingTop="5dp">

    <RelativeLayout
        android:id="@+id/tt_ad_logo"
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:layout_centerVertical="true"
        android:gravity="center_vertical"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_listitem_ad_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="5dp"
        android:layout_toLeftOf="@+id/btn_listitem_creative"
        android:layout_toRightOf="@+id/tt_ad_logo"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLength="14"
        android:singleLine="true"
        android:text="计策略，才真三国！计策略，才真三国！"
        android:textSize="18sp" />


    <Button
        android:id="@+id/btn_listitem_creative"
        android:layout_width="wrap_content"
        android:layout_height="28dp"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_marginStart="3dp"
        android:layout_marginLeft="3dp"
        android:layout_marginEnd="8dp"
        android:layout_marginRight="8dp"
        android:background="@drawable/btn_bg_creative"
        android:gravity="center"
        android:padding="3dp"
        android:text="立即下载"
        android:textColor="#3399cc"
        android:textSize="14sp" />

    <LinearLayout
        android:id="@+id/app_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_listitem_ad_title"
        android:layout_marginTop="10dp"
        android:visibility="gone"
        android:orientation="vertical">

        <TextView
            android:id="@+id/app_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#000"
            android:textSize="14sp"
            tools:text="Nidddddddddd" />

        <TextView
            android:id="@+id/author_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#000"
            android:textSize="14sp"
            tools:text="Nidddddddddd" />

        <TextView
            android:id="@+id/package_size"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#000"
            android:textSize="14sp"
            tools:text="Nidddddddddd" />

        <TextView
            android:id="@+id/permissions_url"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#000"
            android:textSize="14sp"
            tools:text="Nidddddddddd" />

        <TextView
            android:id="@+id/privacy_agreement"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#000"
            android:textSize="14sp"
            tools:text="Nidddddddddd" />

        <TextView
            android:id="@+id/version_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#000"
            android:textSize="14sp"
            tools:text="Nidddddddddd" />

        <TextView
            android:id="@+id/permissions_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#000"
            android:textSize="14sp"
            tools:text="Nidddddddddd" />
    </LinearLayout>
</RelativeLayout>
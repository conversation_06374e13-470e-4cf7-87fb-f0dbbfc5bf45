<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#fff"
        tools:ignore="HardcodedText">

        <ImageView
            android:id="@+id/iv_listitem_image"
            android:layout_width="135dp"
            android:layout_height="240dp"
            android:layout_centerHorizontal="true"
            android:layout_marginEnd="10dp"
            android:layout_marginRight="10dp"
            android:scaleType="centerCrop" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/iv_listitem_image"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_listitem_ad_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:gravity="center"
                android:singleLine="false"
                android:textSize="14sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal">

                    <ImageView
                        android:id="@+id/iv_listitem_icon"
                        android:layout_width="30dp"
                        android:layout_height="40dp"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="10dp"
                        android:layout_marginLeft="10dp" />

                    <LinearLayout
                        android:id="@+id/tv_source_desc_layout"
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/layout_image_group"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="5dp"
                        android:layout_marginLeft="5dp"
                        android:layout_toEndOf="@id/iv_listitem_icon"
                        android:layout_toRightOf="@id/iv_listitem_icon"
                        android:orientation="vertical"
                        tools:ignore="NotSibling">

                        <TextView
                            android:id="@+id/tv_listitem_ad_desc"
                            android:layout_width="80dp"
                            android:layout_height="20dp"
                            android:ellipsize="end"
                            android:singleLine="true"
                            android:textSize="10sp" />

                        <TextView
                            android:id="@+id/tv_listitem_ad_source"
                            android:layout_width="100dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:layout_marginLeft="10dp"
                            android:layout_marginBottom="10dp"
                            android:ellipsize="end"
                            android:singleLine="true"
                            android:textSize="8sp" />

                    </LinearLayout>

                </RelativeLayout>

                <Button
                    android:id="@+id/btn_listitem_creative"
                    android:layout_width="135dp"
                    android:layout_height="40dp"
                    android:textSize="9sp" />

                <LinearLayout
                    android:id="@+id/app_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/app_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#000"
                        android:textSize="14sp"
                        tools:text="Nidddddddddd" />

                    <TextView
                        android:id="@+id/author_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#000"
                        android:textSize="14sp"
                        tools:text="Nidddddddddd" />

                    <TextView
                        android:id="@+id/package_size"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#000"
                        android:textSize="14sp"
                        tools:text="Nidddddddddd" />

                    <TextView
                        android:id="@+id/permissions_url"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#000"
                        android:textSize="14sp"
                        tools:text="Nidddddddddd" />

                    <TextView
                        android:id="@+id/privacy_agreement"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#000"
                        android:textSize="14sp"
                        tools:text="Nidddddddddd" />

                    <TextView
                        android:id="@+id/version_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#000"
                        android:textSize="14sp"
                        tools:text="Nidddddddddd" />

                    <TextView
                        android:id="@+id/permissions_content"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="#000"
                        android:textSize="14sp"
                        tools:text="Nidddddddddd" />
                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <ImageView
            android:id="@+id/iv_listitem_dislike"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"
            android:layout_marginTop="10dp"
            android:layout_marginRight="10dp"
            android:clickable="true"
            android:src="@drawable/dislike_icon" />

    </RelativeLayout>

</FrameLayout>
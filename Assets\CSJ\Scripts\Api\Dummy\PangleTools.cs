﻿#if UNITY_EDITOR || (!UNITY_ANDROID && !UNITY_IOS)

namespace ByteDance.Union
{
    //UnityEngine.Screen
    /// <summary>
    /// The feed Ad.
    /// </summary>
    public class PangleTools
    {
        /// <summary>
        /// getScreenScale
        /// </summary>
        static public float getScreenScale() { return 0; }
        /// <summary>
        /// getScreenWidth
        /// </summary>
        static public float getScreenWidth() { return 0; }
        /// <summary>
        /// getScreenWidth
        /// </summary>
        static public float getScreenHeight() { return 0; }
        /// <summary>
        /// getWindowSafeAreaInsetsTop
        /// </summary>
        static public float getWindowSafeAreaInsetsTop() { return 0; }
        /// <summary>
        /// getWindowSafeAreaInsetsLeft
        /// </summary>
        static public float getWindowSafeAreaInsetsLeft() { return 0; }
        /// <summary>
        /// getWindowSafeAreaInsetsBottom
        /// </summary>
        static public float getWindowSafeAreaInsetsBottom() { return 0; }
        /// <summary>
        /// getWindowSafeAreaInsetsRight
        /// </summary>
        static public float getWindowSafeAreaInsetsRight() { return 0; }
    }
}
#endif

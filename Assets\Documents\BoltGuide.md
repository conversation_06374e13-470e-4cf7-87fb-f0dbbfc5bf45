# Unity Bolt互动视频游戏制作指南

## 一、基础设置

### 1. 安装Visual Scripting
1. 打开Package Manager (Window > Package Manager)
2. 搜索并安装 "Visual Scripting"
3. 等待安装完成后，Unity会自动重新编译

### 2. 初始化Visual Scripting
1. 打开 Edit > Project Settings > Visual Scripting
2. 点击 "Regenerate Units" 按钮
3. 点击 "Regenerate Type Options" 按钮

### 3. 创建基础场景结构
1. 在Hierarchy中创建以下对象：
   ```
   - VideoPlayer (Empty GameObject)
     |- Video Screen (Quad)
   - ChoiceManager (Empty GameObject)
   - UICanvas (UI > Canvas)
     |- ChoicePanel (UI > Panel)
       |- Choice1 (UI > Button)
       |- Choice2 (UI > Button)
       |- Choice3 (UI > Button)
   ```

## 二、视频播放器设置

### 1. 设置视频屏幕
1. 选择Video Screen (Quad)对象
2. 在Inspector中：
   - 调整Scale为适合的屏幕比例（如16:9）
   - 添加新材质：
     - 创建新材质（右键 Assets > Create > Material）
     - 将Shader改为 "Unlit/Texture"

### 2. 设置视频播放器
1. 选择VideoPlayer对象
2. 添加组件：
   - Add Component > Video > Video Player
   - Add Component > Visual Scripting > Script Machine
3. 配置Video Player组件：
   - Render Mode: Render Texture
   - 创建新的RenderTexture（右键 Assets > Create > Render Texture）
   - 将RenderTexture拖到Video Player的Target Texture
   - 将同一个RenderTexture拖到Video Screen的材质中

## 三、使用Bolt创建视频控制逻辑

### 1. 创建视频状态图
1. 在Project窗口中：
   - 右键 > Create > Visual Scripting > Script Graph
   - 命名为 "VideoController"
2. 双击打开图形编辑器
3. 添加基本节点：
   - Events > Start
   - Events > Update
   - GameObject > GetComponent > VideoPlayer

### 2. 创建视频加载逻辑
1. 在图形编辑器中：
   ```
   Start Event -> GetComponent(VideoPlayer) -> Set URL
                                           -> Play
   ```
2. 创建变量：
   - 在Variables面板中添加：
     - VideoClips (List<String>)：存储视频路径
     - CurrentVideoIndex (Integer)：当前播放的视频索引

### 3. 创建选项系统
1. 创建新的Script Graph："ChoiceManager"
2. 添加选项显示逻辑：
   ```
   视频播放结束事件 -> 显示选项面板
   选项按钮点击事件 -> 隐藏选项面板 -> 加载下一个视频
   ```

## 四、制作具体剧情节点

### 1. 创建第一个场景
1. 导入视频文件：
   - 将视频文件放入Assets/Videos文件夹
   - 确保视频格式为MP4

2. 设置开场视频：
   ```
   在VideoController图表中：
   Start -> GetComponent(VideoPlayer) 
        -> Set URL ("Assets/Videos/opening.mp4")
        -> Play
   ```

3. 添加选项触发器：
   ```
   在VideoController图表中：
   Update -> GetComponent(VideoPlayer)
         -> Get time
         -> Compare(>5) // 5秒后显示选项
         -> Show Choices
   ```

### 2. 创建选项逻辑
1. 在ChoiceManager图表中：
   ```
   Button Click Event -> Hide All Choices
                     -> Set Next Video URL
                     -> Play Next Video
   ```

2. 设置选项变量：
   - 创建变量存储选项信息：
     - ChoiceText (String)
     - NextVideoPath (String)
     - RequiredItems (List<String>)

### 3. 添加条件判断
1. 创建条件检查节点：
   ```
   在ChoiceManager图表中：
   Before Show Choice -> Check Conditions
                     -> If True -> Show Choice
                     -> If False -> Hide Choice
   ```

## 五、示例：制作一个简单的分支

### 1. 准备素材
1. 准备以下视频文件：
   - opening.mp4（开场视频）
   - path_a.mp4（选择A后的视频）
   - path_b.mp4（选择B后的视频）

2. 创建选项按钮：
   - 在ChoicePanel中设置两个按钮
   - 设置按钮文本和样式

### 2. 创建视频流程
1. 在VideoController中设置视频序列：
   ```
   Variables:
   - VideoClips = ["opening", "path_a", "path_b"]
   - CurrentIndex = 0
   ```

2. 添加视频切换逻辑：
   ```
   选项点击事件 -> Get Choice Data
                -> Set CurrentIndex
                -> Load Video
                -> Play
   ```

### 3. 添加变量跟踪
1. 创建游戏状态变量：
   ```
   Variables:
   - HasKey (Boolean)
   - ExploredLeft (Boolean)
   - ExploredRight (Boolean)
   ```

2. 添加变量更新逻辑：
   ```
   选项选择事件 -> Update Variables
                -> Check New Conditions
                -> Update Available Choices
   ```

## 六、测试和调试

### 1. 运行时测试
1. 在Play模式下测试：
   - 检查视频是否正常播放
   - 验证选项是否正确显示
   - 测试选项选择后的跳转

### 2. 使用Debug节点
1. 添加调试信息：
   ```
   在关键节点后添加：
   -> Debug Log(当前状态)
   -> Debug Log(变量值)
   ```

### 3. 常见问题解决
- 视频不播放：
  - 检查Video Player组件设置
  - 确认RenderTexture连接正确
  - 验证视频文件路径

- 选项不显示：
  - 检查触发条件是否正确
  - 验证UI层级和Canvas设置
  - 确认事件连接完整

- 变量不更新：
  - 使用Debug节点检查变量值
  - 验证变量更新节点连接
  - 检查条件判断逻辑 
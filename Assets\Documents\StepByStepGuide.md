# 互动视频游戏制作步骤指南

## 一、Unity项目初始设置

### 1. 创建基础目录结构
1. 在Assets目录下创建以下文件夹：
   ```
   Assets/
   ├── Scripts/
   ├── UI/
   ├── Scenes/
   ├── StreamingAssets/
   │   ├── Videos/
   │   └── GameData/
   └── Resources/
   ```

### 2. 导入必要的包
1. 打开Package Manager (Window > Package Manager)
2. 添加以下包：
   - UI Toolkit
   - Video Player
   - JSON .NET for Unity

## 二、场景搭建步骤

### 1. 主菜单场景搭建
1. 创建新场景：
   - 右键 Scenes 文件夹 > Create > Scene
   - 命名为 "MainMenu"

2. 创建UI画布：
   - 右键Hierarchy > UI > UI Document
   - 在Inspector中设置UI Document组件：
     - Source Asset: 选择 `MainMenuUI.uxml`
     - Style Sheet: 选择 `MainMenuUI.uss`

3. 添加控制器：
   - 选择UI Document对象
   - Add Component > MainMenuController
   - 设置引用：
     - Menu Document: 拖拽UI Document组件
     - Game Scene Name: 输入 "GameScene"

### 2. 游戏场景搭建
1. 创建新场景：
   - 右键 Scenes 文件夹 > Create > Scene
   - 命名为 "GameScene"

2. 创建管理器对象：
   - 创建空对象：右键Hierarchy > Create Empty
   - 命名为 "Managers"
   - 添加以下组件：
     - VideoManager
     - GameStateManager
     - DataManager
     - VariableManager

3. 创建UI：
   - 右键Hierarchy > UI > UI Document
   - 设置UI Document组件：
     - Source Asset: 选择 `GameUI.uxml`
     - Style Sheet: 选择 `GameUI.uss`
   - 添加GameUIController组件
   - 设置引用：
     - Game UI Document: 拖拽UI Document组件
     - Video Controller: 拖拽VideoPlayController组件

## 三、视频节点制作流程

### 1. 准备视频文件
1. 视频处理：
   - 使用视频编辑软件将视频剪辑为所需片段
   - 确保每个片段都有合适的开始和结束点
   - 导出为MP4格式，建议使用H.264编码

2. 放置视频文件：
   - 将视频文件复制到 `StreamingAssets/Videos` 目录
   - 建议使用有意义的名称，如：
     ```
     opening.mp4
     path_a_1.mp4
     path_a_2.mp4
     path_b_1.mp4
     ```

### 2. 创建节点配置
1. 创建VideoNodes.json：
   ```json
   [
     {
       "nodeId": "start",
       "videoClipPath": "Videos/opening.mp4",
       "choiceNodeIds": ["choice_1", "choice_2"]
     },
     {
       "nodeId": "path_a_1",
       "videoClipPath": "Videos/path_a_1.mp4",
       "autoPlayNext": true,
       "nextNodeId": "path_a_2"
     }
   ]
   ```

2. 创建ChoiceNodes.json：
   ```json
   [
     {
       "nodeId": "choice_1",
       "choiceText": "选择A路径",
       "nextVideoNodeId": "path_a_1",
       "appearTime": 5.0
     },
     {
       "nodeId": "choice_2",
       "choiceText": "选择B路径",
       "nextVideoNodeId": "path_b_1",
       "appearTime": 5.0
     }
   ]
   ```

### 3. 节点连接示例
以下是一个简单的分支剧情示例：

1. 开场视频节点：
```json
{
    "nodeId": "start",
    "videoClipPath": "Videos/opening.mp4",
    "choiceNodeIds": ["choice_door_left", "choice_door_right"]
}
```

2. 选项节点：
```json
{
    "nodeId": "choice_door_left",
    "choiceText": "打开左边的门",
    "requiredVariables": {
        "hasKey": true
    },
    "nextVideoNodeId": "path_left"
}
```

3. 后续视频节点：
```json
{
    "nodeId": "path_left",
    "videoClipPath": "Videos/left_door.mp4",
    "autoPlayNext": true,
    "nextNodeId": "path_left_2"
}
```

## 四、变量系统使用示例

### 1. 创建初始变量
在VariableManager的InitializeDefaultVariables方法中：
```csharp
private void InitializeDefaultVariables()
{
    SetVariable("hasKey", false);
    SetVariable("exploredLeft", false);
    SetVariable("exploredRight", false);
    SetVariable("friendshipLevel", 0);
}
```

### 2. 条件判断示例
1. 需要钥匙的门：
```json
{
    "nodeId": "choice_locked_door",
    "choiceText": "打开锁着的门",
    "requiredVariables": {
        "hasKey": true
    },
    "variableEffects": {
        "hasKey": false,
        "doorUnlocked": true
    }
}
```

2. 基于好感度的选项：
```json
{
    "nodeId": "choice_friend",
    "choiceText": "请求帮助",
    "requiredVariables": {
        "friendshipLevel": 50
    }
}
```

## 五、测试示例剧情

### 1. 简单的测试剧情
1. 创建以下视频：
   - opening.mp4：主角在十字路口
   - path_a.mp4：左边路径的内容
   - path_b.mp4：右边路径的内容

2. 配置节点：
   ```json
   // VideoNodes.json
   [
     {
       "nodeId": "start",
       "videoClipPath": "Videos/opening.mp4",
       "choiceNodeIds": ["go_left", "go_right"]
     },
     {
       "nodeId": "path_a",
       "videoClipPath": "Videos/path_a.mp4",
       "choiceNodeIds": ["return_start"]
     },
     {
       "nodeId": "path_b",
       "videoClipPath": "Videos/path_b.mp4",
       "choiceNodeIds": ["return_start"]
     }
   ]

   // ChoiceNodes.json
   [
     {
       "nodeId": "go_left",
       "choiceText": "向左走",
       "nextVideoNodeId": "path_a"
     },
     {
       "nodeId": "go_right",
       "choiceText": "向右走",
       "nextVideoNodeId": "path_b"
     },
     {
       "nodeId": "return_start",
       "choiceText": "返回起点",
       "nextVideoNodeId": "start"
     }
   ]
   ```

### 2. 运行测试
1. 打开MainMenu场景
2. 点击Play按钮
3. 测试流程：
   - 点击"新游戏"
   - 观看开场视频
   - 选择不同的选项
   - 验证视频切换是否正确
   - 测试返回功能

## 六、常见问题解决

### 1. 视频不播放
- 检查视频文件是否在正确位置
- 确认视频格式是否支持
- 查看Console窗口的错误信息

### 2. 选项不显示
- 检查choiceNodeIds是否正确
- 验证选项的时间设置
- 确认条件变量是否满足

### 3. 场景切换问题
- 确保场景已添加到Build Settings
- 检查场景名称是否正确
- 验证场景索引设置 
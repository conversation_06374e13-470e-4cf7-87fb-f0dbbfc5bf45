# 互动视频游戏框架使用手册

## 一、系统概述

本框架是一个基于Unity的互动视频游戏制作工具，支持：
- 分支剧情创作
- 条件选择系统
- 变量跟踪
- 存档读取
- 自定义UI界面

## 二、剧情制作指南

### 1. 准备视频素材
- 视频格式：建议使用MP4格式，分辨率1920x1080
- 文件位置：将视频文件放置在 `Assets/StreamingAssets/Videos` 目录下
- 命名规则：建议使用有意义的名称，如 `opening.mp4`, `path_a.mp4` 等

### 2. 编写剧情节点
在 `StreamingAssets/GameData/VideoNodes.json` 中定义视频节点：
```json
{
    "nodeId": "opening",                    // 节点唯一标识符
    "videoClipPath": "Videos/opening.mp4",  // 视频文件路径
    "autoPlayNext": false,                  // 是否自动播放下一个视频
    "nextNodeId": "",                       // 自动播放时的下一个节点ID
    "choiceNodeIds": ["choice_1", "choice_2"], // 可用的选项ID列表
    "requiredVariables": {                  // 播放该节点需要满足的条件
        "hasKey": true
    }
}
```

### 3. 创建选项节点
在 `StreamingAssets/GameData/ChoiceNodes.json` 中定义选项：
```json
{
    "nodeId": "choice_1",              // 选项唯一标识符
    "choiceText": "进入左边的门",      // 选项显示文本
    "description": "这扇门看起来很旧", // 选项描述（可选）
    "requiredVariables": {             // 显示该选项需要满足的条件
        "hasKey": true
    },
    "variableEffects": {               // 选择后的变量影响
        "hasKey": false,
        "exploredLeft": true
    },
    "nextVideoNodeId": "path_a",       // 选择后播放的视频节点
    "appearTime": 5.0,                 // 选项出现的时间（-1表示视频结束时）
    "isActive": true                   // 选项是否激活
}
```

### 4. 变量系统使用
- 变量类型支持：
  - 布尔值（true/false）
  - 数字（整数/浮点数）
  - 字符串
  - 列表

- 变量使用场景：
  - 条件判断：控制视频节点和选项的可用性
  - 状态记录：记录玩家的选择和进度
  - 成就系统：追踪玩家的游戏表现

## 三、场景布置指南

### 1. 创建主菜单场景
1. 创建新场景，命名为 "MainMenu"
2. 添加UI组件：
   ```
   - Canvas
     |- UIDocument (添加MainMenuUI.uxml)
     |- MainMenuController脚本
   ```
3. 设置背景视频（可选）：
   - 将视频文件拖入场景
   - 在MainMenuController中设置引用

### 2. 创建游戏场景
1. 创建新场景，命名为 "GameScene"
2. 添加必要组件：
   ```
   - GameManager
   - VideoManager
   - DataManager
   - VariableManager
   - Canvas
     |- UIDocument (添加GameUI.uxml)
     |- GameUIController脚本
   ```

### 3. 场景配置
1. 在Build Settings中添加场景
2. 设置场景索引：
   - MainMenu: 0
   - GameScene: 1

## 四、游戏测试指南

### 1. 运行前检查
- 确保所有视频文件已放置在正确位置
- 检查JSON文件格式是否正确
- 验证所有节点ID是否正确关联

### 2. 测试流程
1. 运行游戏，检查主菜单功能
2. 测试新游戏流程：
   - 视频是否正常播放
   - 选项是否按时出现
   - 条件判断是否正确
3. 测试存档功能：
   - 创建存档
   - 读取存档
   - 验证变量状态

### 3. 常见问题排查
- 视频不播放：检查文件路径和格式
- 选项不显示：检查条件设置和时间点
- 存档无法读取：检查JSON文件权限

## 五、发布注意事项

### 1. 文件打包
- 确保所有视频文件在StreamingAssets目录下
- 检查场景是否已添加到Build Settings
- 验证所有依赖项是否完整

### 2. 性能优化
- 压缩视频文件以减小包体积
- 使用适当的视频编码格式
- 优化资源加载方式

### 3. 平台特定设置
- Windows：
  - 检查视频编解码器支持
  - 设置适当的播放器选项
- WebGL：
  - 确保视频格式兼容
  - 考虑流式加载方案

## 六、最佳实践建议

### 1. 剧情设计
- 保持选项数量适中（3-4个为宜）
- 确保每个分支都有意义
- 适当使用变量创造关联性
- 注意剧情节奏的控制

### 2. 视频制作
- 为选项留出足够的观看和选择时间
- 考虑转场效果的自然性
- 保持视频质量的一致性
- 注意音频水平的平衡

### 3. 变量使用
- 合理规划变量名称和用途
- 避免过于复杂的条件判断
- 及时清理不再使用的变量
- 做好变量的初始化工作

### 4. 测试建议
- 创建测试用例覆盖所有分支
- 验证所有条件组合
- 测试极端情况下的表现
- 收集玩家反馈并持续优化 
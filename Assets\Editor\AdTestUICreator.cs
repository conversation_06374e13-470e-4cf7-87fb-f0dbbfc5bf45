using UnityEngine;
using UnityEditor;
using UnityEngine.UI;

public class AdTestUICreator : EditorWindow
{
    [MenuItem("Tools/UI工具/一键创建广告测试单元")]
    public static void CreateAdTestUI()
    {
        // 检查是否已存在Canvas
        Canvas existingCanvas = FindObjectOfType<Canvas>();
        if (existingCanvas != null)
        {
            bool shouldCreate = EditorUtility.DisplayDialog("警告", 
                "场景中已存在Canvas，是否继续创建新的广告测试UI？\n（建议使用现有Canvas）", 
                "继续创建", "取消");
            
            if (!shouldCreate)
            {
                return;
            }
        }

        // 创建Canvas
        GameObject canvasObj = new GameObject("AdTestCanvas");
        Canvas canvas = canvasObj.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvasObj.AddComponent<UnityEngine.UI.CanvasScaler>();
        canvasObj.AddComponent<UnityEngine.UI.GraphicRaycaster>();

        // 创建按钮容器
        GameObject buttonContainer = new GameObject("ButtonContainer");
        buttonContainer.transform.SetParent(canvasObj.transform, false);
        RectTransform containerRect = buttonContainer.AddComponent<RectTransform>();
        containerRect.anchorMin = new Vector2(0.5f, 0.5f);
        containerRect.anchorMax = new Vector2(0.5f, 0.5f);
        containerRect.pivot = new Vector2(0.5f, 0.5f);
        containerRect.anchoredPosition = Vector2.zero;
        containerRect.sizeDelta = new Vector2(400, 200);

        // 创建开屏广告按钮
        GameObject splashButtonObj = CreateButton("SplashAdButton", "开屏广告", buttonContainer.transform);
        splashButtonObj.GetComponent<RectTransform>().anchoredPosition = new Vector2(0, 50);

        // 创建激励视频按钮
        GameObject rewardButtonObj = CreateButton("RewardAdButton", "激励视频", buttonContainer.transform);
        rewardButtonObj.GetComponent<RectTransform>().anchoredPosition = new Vector2(0, -50);

        // 创建状态面板
        GameObject statusPanelObj = new GameObject("StatusPanel");
        statusPanelObj.transform.SetParent(canvasObj.transform, false);
        RectTransform statusRect = statusPanelObj.AddComponent<RectTransform>();
        statusRect.anchorMin = new Vector2(0.5f, 0.1f);
        statusRect.anchorMax = new Vector2(0.5f, 0.2f);
        statusRect.pivot = new Vector2(0.5f, 0.5f);
        statusRect.anchoredPosition = Vector2.zero;
        statusRect.sizeDelta = new Vector2(400, 60);

        // 添加背景
        Image bgImage = statusPanelObj.AddComponent<Image>();
        bgImage.color = new Color(0, 0, 0, 0.7f);

        // 创建状态文本
        GameObject statusTextObj = new GameObject("StatusText");
        statusTextObj.transform.SetParent(statusPanelObj.transform, false);
        RectTransform textRect = statusTextObj.AddComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;

        Text statusText = statusTextObj.AddComponent<Text>();
        statusText.text = "";
        statusText.alignment = TextAnchor.MiddleCenter;
        statusText.fontSize = 24;
        statusText.color = Color.white;
        statusText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");

        // 添加AdTestUI组件
        AdTestUI adTestUI = canvasObj.AddComponent<AdTestUI>();
        
        // 确保所有引用都被正确设置
        Button splashButton = splashButtonObj.GetComponent<Button>();
        Button rewardButton = rewardButtonObj.GetComponent<Button>();
        
        if (splashButton == null || rewardButton == null || statusText == null || statusPanelObj == null)
        {
            Debug.LogError("[AdTestUI] 创建UI时出现错误：某些组件未正确创建");
            return;
        }
        
        adTestUI.splashAdButton = splashButton;
        adTestUI.rewardAdButton = rewardButton;
        adTestUI.statusText = statusText;
        adTestUI.statusPanel = statusPanelObj;

        // 默认隐藏状态面板
        statusPanelObj.SetActive(false);

        // 保存为预制体
        if (!AssetDatabase.IsValidFolder("Assets/Prefabs"))
        {
            AssetDatabase.CreateFolder("Assets", "Prefabs");
        }
        
        PrefabUtility.SaveAsPrefabAsset(canvasObj, "Assets/Prefabs/AdTestUI.prefab");
        
        // 选中新创建的对象
        Selection.activeGameObject = canvasObj;
        
        Debug.Log("[AdTestUI] 广告测试UI创建完成！");
    }

    private static GameObject CreateButton(string name, string buttonText, Transform parent)
    {
        GameObject buttonObj = new GameObject(name);
        buttonObj.transform.SetParent(parent, false);
        
        // 设置RectTransform
        RectTransform rectTransform = buttonObj.AddComponent<RectTransform>();
        rectTransform.sizeDelta = new Vector2(200, 60);
        
        // 添加Image组件
        Image image = buttonObj.AddComponent<Image>();
        image.color = new Color(0.2f, 0.2f, 0.2f, 1f);
        
        // 添加Button组件
        Button button = buttonObj.AddComponent<Button>();
        button.targetGraphic = image;
        
        // 创建文本对象
        GameObject textObj = new GameObject("Text");
        textObj.transform.SetParent(buttonObj.transform, false);
        RectTransform textRect = textObj.AddComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
        
        Text text = textObj.AddComponent<Text>();
        text.text = buttonText;
        text.alignment = TextAnchor.MiddleCenter;
        text.fontSize = 24;
        text.color = Color.white;
        text.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        
        return buttonObj;
    }
} 
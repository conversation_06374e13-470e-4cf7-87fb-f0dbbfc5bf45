using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using VideoGameFramework.Models;
using UnityEngine.Video;

/// <summary>
/// 商品编辑器窗口
/// </summary>
public class ShopEditor : EditorWindow
{
    private Vector2 scrollPosition;
    private ShopData shopData;
    private string selectedCategoryId;
    private string newCategoryName = "";
    private string newItemName = "";
    private int newItemPrice = 0;
    private string newItemDescription = "";
    private Sprite newItemSprite;
    private string newItemIconPath = "";
    private bool showAddCategory = false;
    private bool showAddItem = false;
    private string jsonPath = "Assets/Resources/ShopData.json";

    // 变量效果编辑相关
    private string newEffectVariableName = "";
    private float newEffectValue = 0;
    private VariableEffectOperation newEffectOperation = VariableEffectOperation.Set;
    private bool showAddEffect = false;

    private int testAdCount = 0;

    // 新增：商品折叠状态字典
    private Dictionary<string, bool> itemFoldoutStates = new Dictionary<string, bool>();

    // 新增：拖拽相关变量
    private int dragSourceIndex = -1;
    private int dragTargetIndex = -1;
    private string dragItemId = null;

    [MenuItem("Window/游戏系统/商品编辑器")]
    public static void ShowWindow()
    {
        GetWindow<ShopEditor>("商品编辑器");
    }

    private void OnEnable()
    {
        LoadShopData();
    }

    private void LoadShopData()
    {
        // 尝试从Resources文件夹加载
        TextAsset textAsset = Resources.Load<TextAsset>("ShopData");
        if (textAsset != null)
        {
            shopData = JsonUtility.FromJson<ShopData>(textAsset.text);
        }
        else
        {
            // 如果Resources中没有，尝试从指定路径加载
            if (File.Exists(jsonPath))
            {
                string jsonContent = File.ReadAllText(jsonPath);
                shopData = JsonUtility.FromJson<ShopData>(jsonContent);
            }
            else
            {
                // 如果文件不存在，创建新的数据
                shopData = new ShopData { categories = new List<ShopCategory>() };
            }
        }
    }

    private void SaveShopData()
    {
        string jsonContent = JsonUtility.ToJson(shopData, true);
        File.WriteAllText(jsonPath, jsonContent);
        AssetDatabase.Refresh();
        Debug.Log("[ShopEditor] 商店数据已保存");
    }

    private void OnGUI()
    {
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

        // ====== 广告计数测试接口 ======
        EditorGUILayout.Space(10);
        EditorGUILayout.LabelField("[测试] 广告计数设置", EditorStyles.boldLabel);
        if (ShopManager.Instance != null)
        {
            int currentAdCount = ShopManager.Instance.GetTotalAdCount();
            EditorGUILayout.LabelField($"当前广告计数: {currentAdCount}");
            testAdCount = EditorGUILayout.IntField("设置广告计数", testAdCount);
            if (GUILayout.Button("应用广告计数"))
            {
                // 直接设置广告计数
                typeof(ShopManager)
                    .GetField("totalAdCount", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                    .SetValue(ShopManager.Instance, testAdCount);
                // 强制保存并刷新
                typeof(ShopManager)
                    .GetMethod("SaveAdCount", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                    .Invoke(ShopManager.Instance, null);
                // 更新解锁状态
                typeof(ShopManager)
                    .GetMethod("UpdateVideoUnlockStatus", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                    .Invoke(ShopManager.Instance, null);
                
                // 添加调试日志
                Debug.Log($"[ShopEditor] 已设置广告计数为: {testAdCount}");
                foreach (var category in shopData.categories)
                {
                    if (category.items != null)
                    {
                        foreach (var item in category.items)
                        {
                            if (item.isVideoReward)
                            {
                                Debug.Log($"[ShopEditor] 商品 {item.name} 的解锁状态: {item.isVideoUnlocked}, 所需广告次数: {item.requiredAdCount}");
                            }
                        }
                    }
                }
                
                // 保存商店数据
                SaveShopData();
            }
        }
        else
        {
            EditorGUILayout.HelpBox("ShopManager.Instance 不存在，无法设置广告计数。", MessageType.Warning);
        }

        // 显示添加分类的UI
        EditorGUILayout.Space(10);
        showAddCategory = EditorGUILayout.Foldout(showAddCategory, "添加新分类");
        if (showAddCategory)
        {
            EditorGUILayout.BeginHorizontal();
            newCategoryName = EditorGUILayout.TextField("分类名称", newCategoryName);
            if (GUILayout.Button("添加", GUILayout.Width(60)))
            {
                if (!string.IsNullOrEmpty(newCategoryName))
                {
                    AddCategory(newCategoryName);
                    newCategoryName = "";
                }
            }
            EditorGUILayout.EndHorizontal();
        }

        // 显示分类列表
        EditorGUILayout.Space(10);
        EditorGUILayout.LabelField("商品分类", EditorStyles.boldLabel);
        foreach (var category in shopData.categories)
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            
            // 分类标题和删除按钮
            EditorGUILayout.BeginHorizontal();
            bool isSelected = category.id == selectedCategoryId;
            bool newSelected = EditorGUILayout.Toggle(isSelected, GUILayout.Width(20));
            if (newSelected != isSelected)
            {
                selectedCategoryId = newSelected ? category.id : null;
            }
            
            EditorGUILayout.LabelField(category.name, EditorStyles.boldLabel);
            // 显示分类ID，便于复制
            EditorGUILayout.SelectableLabel($"ID: {category.id}", EditorStyles.miniLabel, GUILayout.Height(16));
            if (GUILayout.Button("删除分类", GUILayout.Width(80)))
            {
                if (EditorUtility.DisplayDialog("确认删除", 
                    $"确定要删除分类 '{category.name}' 吗？\n这将删除该分类下的所有商品！", 
                    "删除", "取消"))
                {
                    shopData.categories.Remove(category);
                    SaveShopData();
                    break;
                }
            }
            EditorGUILayout.EndHorizontal();

            // 如果分类被选中，显示其商品列表
            if (selectedCategoryId == category.id)
            {
                EditorGUI.indentLevel++;
                
                // 显示添加商品的UI
                showAddItem = EditorGUILayout.Foldout(showAddItem, "添加新商品");
                if (showAddItem)
                {
                    EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                    newItemName = EditorGUILayout.TextField("商品名称", newItemName);
                    newItemPrice = EditorGUILayout.IntField("价格", newItemPrice);
                    newItemDescription = EditorGUILayout.TextField("商品描述", newItemDescription);
                    
                    EditorGUI.BeginChangeCheck();
                    newItemSprite = (Sprite)EditorGUILayout.ObjectField("商品图标", newItemSprite, typeof(Sprite), false);
                    if (EditorGUI.EndChangeCheck() && newItemSprite != null)
                    {
                        string assetPath = AssetDatabase.GetAssetPath(newItemSprite);
                        newItemIconPath = GetResourcePath(assetPath);
                    }
                    
                    if (GUILayout.Button("添加商品"))
                    {
                        if (!string.IsNullOrEmpty(newItemName))
                        {
                            AddItem(category, newItemName, newItemPrice, newItemDescription, newItemIconPath);
                            newItemName = "";
                            newItemPrice = 0;
                            newItemDescription = "";
                            newItemSprite = null;
                            newItemIconPath = "";
                        }
                    }
                    EditorGUILayout.EndVertical();
                }

                // 显示商品列表
                if (category.items != null)
                {
                    EditorGUILayout.LabelField("商品列表", EditorStyles.boldLabel);

                    // 拖拽排序相关
                    Event evt = Event.current;
                    Rect dropArea = GUILayoutUtility.GetRect(0, 0, GUILayout.ExpandWidth(true), GUILayout.Height(0));

                    for (int i = 0; i < category.items.Count; i++)
                    {
                        var item = category.items[i];

                        // 初始化折叠状态
                        if (!itemFoldoutStates.ContainsKey(item.id))
                            itemFoldoutStates[item.id] = false;

                        // 商品外层
                        Rect itemRect = EditorGUILayout.BeginVertical(EditorStyles.helpBox);

                        // 折叠/展开+拖拽手柄
                        EditorGUILayout.BeginHorizontal();

                        // 拖拽手柄（只在点击此处时才响应拖拽）
                        Rect dragHandleRect = GUILayoutUtility.GetRect(18, 18, GUILayout.Width(18), GUILayout.Height(18));
                        GUI.Label(dragHandleRect, EditorGUIUtility.IconContent("d_TransformTool"), EditorStyles.label);

                        // 拖拽排序：检测拖拽事件（只在拖拽手柄区域内）
                        if (evt.type == EventType.MouseDown && dragHandleRect.Contains(evt.mousePosition))
                        {
                            dragSourceIndex = i;
                            dragItemId = item.id;
                            DragAndDrop.PrepareStartDrag();
                            DragAndDrop.objectReferences = new UnityEngine.Object[0];
                            DragAndDrop.SetGenericData("ShopItem", item);
                            DragAndDrop.StartDrag("ShopItemDrag");
                            evt.Use();
                        }
                        if ((evt.type == EventType.DragUpdated || evt.type == EventType.DragPerform)
                            && dragHandleRect.Contains(evt.mousePosition)
                            && DragAndDrop.GetGenericData("ShopItem") != null)
                        {
                            DragAndDrop.visualMode = DragAndDropVisualMode.Move;
                            dragTargetIndex = i;
                            if (evt.type == EventType.DragPerform)
                            {
                                DragAndDrop.AcceptDrag();
                                if (dragSourceIndex != -1 && dragTargetIndex != -1 && dragSourceIndex != dragTargetIndex)
                                {
                                    var draggedItem = category.items[dragSourceIndex];
                                    category.items.RemoveAt(dragSourceIndex);
                                    if (dragTargetIndex > dragSourceIndex) dragTargetIndex--;
                                    category.items.Insert(dragTargetIndex, draggedItem);
                                    SaveShopData();
                                }
                                dragSourceIndex = -1;
                                dragTargetIndex = -1;
                                dragItemId = null;
                            }
                            evt.Use();
                        }

                        // 折叠/展开按钮（点击商品名可展开/收起）
                        itemFoldoutStates[item.id] = EditorGUILayout.Foldout(itemFoldoutStates[item.id], item.name, false);

                        EditorGUILayout.EndHorizontal();

                        // 展开时显示详细信息
                        if (itemFoldoutStates[item.id])
                        {
                            // ====== 以下为原有商品详细信息UI ======
                            EditorGUILayout.BeginHorizontal();
                            item.name = EditorGUILayout.TextField("名称", item.name);
                            item.price = EditorGUILayout.IntField("价格", item.price);
                            item.isOneTime = EditorGUILayout.Toggle("一次性商品", item.isOneTime);
                            item.isRepeatablePurchase = EditorGUILayout.Toggle("可重复购买", item.isRepeatablePurchase);
                            EditorGUILayout.EndHorizontal();

                            item.description = EditorGUILayout.TextField("描述", item.description);
                            
                            // 替换文本输入为图片选择器
                            Sprite currentSprite = null;
                            if (!string.IsNullOrEmpty(item.iconPath))
                            {
                                currentSprite = Resources.Load<Sprite>(item.iconPath);
                            }
                            
                            EditorGUI.BeginChangeCheck();
                            currentSprite = (Sprite)EditorGUILayout.ObjectField("商品图标", currentSprite, typeof(Sprite), false);
                            if (EditorGUI.EndChangeCheck() && currentSprite != null)
                            {
                                string assetPath = AssetDatabase.GetAssetPath(currentSprite);
                                item.iconPath = GetResourcePath(assetPath);
                            }

                            // 视频展示相关设置
                            EditorGUILayout.Space(5);
                            EditorGUILayout.LabelField("奖励设置", EditorStyles.boldLabel);
                            
                            // 奖励类型选择
                            EditorGUILayout.BeginHorizontal();
                            item.isVideoReward = EditorGUILayout.Toggle("视频奖励", item.isVideoReward);
                            if (!item.isVideoReward)
                            {
                                EditorGUILayout.LabelField("图片奖励");
                            }
                            EditorGUILayout.EndHorizontal();
                            
                            if (item.isVideoReward)
                            {
                                // 视频封面
                                Sprite videoCoverSprite = null;
                                if (!string.IsNullOrEmpty(item.videoCoverPath))
                                {
                                    videoCoverSprite = Resources.Load<Sprite>(item.videoCoverPath);
                                }
                                
                                EditorGUI.BeginChangeCheck();
                                videoCoverSprite = (Sprite)EditorGUILayout.ObjectField("视频封面", videoCoverSprite, typeof(Sprite), false);
                                if (EditorGUI.EndChangeCheck() && videoCoverSprite != null)
                                {
                                    string assetPath = AssetDatabase.GetAssetPath(videoCoverSprite);
                                    item.videoCoverPath = GetResourcePath(assetPath);
                                }
                                
                                // 视频内容
                                VideoClip videoContent = null;
                                if (!string.IsNullOrEmpty(item.videoContentPath))
                                {
                                    videoContent = Resources.Load<VideoClip>(item.videoContentPath);
                                }
                                
                                EditorGUI.BeginChangeCheck();
                                videoContent = (VideoClip)EditorGUILayout.ObjectField("视频内容", videoContent, typeof(VideoClip), false);
                                if (EditorGUI.EndChangeCheck() && videoContent != null)
                                {
                                    string assetPath = AssetDatabase.GetAssetPath(videoContent);
                                    item.videoContentPath = GetResourcePath(assetPath);
                                }
                            }
                            else
                            {
                                // 图片奖励
                                Sprite imageReward = null;
                                if (!string.IsNullOrEmpty(item.iconPath))
                                {
                                    imageReward = Resources.Load<Sprite>(item.iconPath);
                                }
                                
                                EditorGUI.BeginChangeCheck();
                                imageReward = (Sprite)EditorGUILayout.ObjectField("奖励图片", imageReward, typeof(Sprite), false);
                                if (EditorGUI.EndChangeCheck() && imageReward != null)
                                {
                                    string assetPath = AssetDatabase.GetAssetPath(imageReward);
                                    item.iconPath = GetResourcePath(assetPath);
                                }
                            }
                            
                            // 所需广告次数
                            item.requiredAdCount = EditorGUILayout.IntField("所需广告次数", item.requiredAdCount);

                            // 战令相关设置
                            EditorGUILayout.Space(5);
                            EditorGUILayout.LabelField("战令设置", EditorStyles.boldLabel);
                            
                            item.isBattlePassItem = EditorGUILayout.Toggle("是否为战令商品", item.isBattlePassItem);
                            if (item.isBattlePassItem)
                            {
                                item.battlePassTier = EditorGUILayout.IntField("战令等级", item.battlePassTier);
                            }

                            // 变量效果列表
                            EditorGUILayout.Space(5);
                            EditorGUILayout.LabelField("变量效果", EditorStyles.boldLabel);
                            
                            // 显示添加效果的UI
                            showAddEffect = EditorGUILayout.Foldout(showAddEffect, "添加变量效果");
                            if (showAddEffect)
                            {
                                EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                                newEffectVariableName = EditorGUILayout.TextField("变量名", newEffectVariableName);
                                newEffectValue = EditorGUILayout.FloatField("值", newEffectValue);
                                newEffectOperation = (VariableEffectOperation)EditorGUILayout.EnumPopup("操作", newEffectOperation);
                                
                                if (GUILayout.Button("添加效果"))
                                {
                                    if (!string.IsNullOrEmpty(newEffectVariableName))
                                    {
                                        AddEffect(item, newEffectVariableName, newEffectValue, newEffectOperation);
                                        newEffectVariableName = "";
                                        newEffectValue = 0;
                                        newEffectOperation = VariableEffectOperation.Set;
                                    }
                                }
                                EditorGUILayout.EndVertical();
                            }

                            // 显示效果列表
                            if (item.effects != null)
                            {
                                for (int j = item.effects.Count - 1; j >= 0; j--)
                                {
                                    var effect = item.effects[j];
                                    EditorGUILayout.BeginHorizontal(EditorStyles.helpBox);
                                    
                                    EditorGUILayout.LabelField($"变量: {effect.variableName}");
                                    EditorGUILayout.LabelField($"操作: {effect.operation}");
                                    EditorGUILayout.LabelField($"值: {effect.value}");
                                    
                                    if (GUILayout.Button("删除", GUILayout.Width(60)))
                                    {
                                        item.effects.RemoveAt(j);
                                        SaveShopData();
                                        break;
                                    }
                                    
                                    EditorGUILayout.EndHorizontal();
                                }
                            }

                            // 购买条件编辑
                            EditorGUILayout.Space(5);
                            EditorGUILayout.LabelField("购买条件", EditorStyles.boldLabel);
                            if (item.purchaseConditions == null)
                                item.purchaseConditions = new List<PurchaseConditionData>();
                            for (int c = 0; c < item.purchaseConditions.Count; c++)
                            {
                                var cond = item.purchaseConditions[c];
                                EditorGUILayout.BeginHorizontal(EditorStyles.helpBox);
                                // 条件类型选择
                                int typeIndex = cond.type == "coin" ? 0 : 1;
                                typeIndex = EditorGUILayout.Popup("类型", typeIndex, new string[] { "coin", "variable_greater" });
                                cond.type = typeIndex == 0 ? "coin" : "variable_greater";
                                // 参数输入
                                if (cond.type == "coin")
                                {
                                    cond.intValue = EditorGUILayout.IntField("所需金币", cond.intValue);
                                }
                                else if (cond.type == "variable_greater")
                                {
                                    cond.key = EditorGUILayout.TextField("变量名", cond.key);
                                    cond.intValue = EditorGUILayout.IntField("阈值", cond.intValue);
                                }
                                // 删除按钮
                                if (GUILayout.Button("删除", GUILayout.Width(60)))
                                {
                                    item.purchaseConditions.RemoveAt(c);
                                    break;
                                }
                                EditorGUILayout.EndHorizontal();
                            }
                            if (GUILayout.Button("添加购买条件"))
                            {
                                if (item.purchaseConditions == null)
                                    item.purchaseConditions = new List<PurchaseConditionData>();
                                item.purchaseConditions.Add(new PurchaseConditionData { type = "coin", intValue = 0 });
                            }

                            // ====== 卡包相关UI ======
                            EditorGUILayout.Space(5);
                            EditorGUILayout.LabelField("卡包设置", EditorStyles.boldLabel);
                            item.isCardPack = EditorGUILayout.Toggle("是否为卡包", item.isCardPack);
                            if (item.isCardPack)
                            {
                                if (item.cardItems == null) item.cardItems = new List<CardItem>();
                                EditorGUILayout.LabelField("卡牌列表", EditorStyles.boldLabel);
                                for (int k = 0; k < item.cardItems.Count; k++)
                                {
                                    var card = item.cardItems[k];
                                    EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                                    card.cardName = EditorGUILayout.TextField("卡牌名称", card.cardName);
                                    card.cardDescription = EditorGUILayout.TextField("卡牌描述", card.cardDescription);
                                    // 图片列表
                                    if (card.imagePaths == null) card.imagePaths = new List<string>();
                                    EditorGUILayout.LabelField("图片列表");
                                    for (int m = 0; m < card.imagePaths.Count; m++)
                                    {
                                        EditorGUILayout.BeginHorizontal();
                                        Sprite cardSprite = null;
                                        if (!string.IsNullOrEmpty(card.imagePaths[m]))
                                            cardSprite = Resources.Load<Sprite>(card.imagePaths[m]);
                                        EditorGUI.BeginChangeCheck();
                                        cardSprite = (Sprite)EditorGUILayout.ObjectField($"图片{m+1}", cardSprite, typeof(Sprite), false);
                                        if (EditorGUI.EndChangeCheck() && cardSprite != null)
                                        {
                                            string assetPath = AssetDatabase.GetAssetPath(cardSprite);
                                            card.imagePaths[m] = GetResourcePath(assetPath);
                                        }
                                        if (GUILayout.Button("删除图片", GUILayout.Width(60)))
                                        {
                                            card.imagePaths.RemoveAt(m);
                                            m--;
                                        }
                                        EditorGUILayout.EndHorizontal();
                                    }
                                    if (GUILayout.Button("添加图片"))
                                    {
                                        card.imagePaths.Add("");
                                    }
                                    // 视频列表
                                    if (card.videoPaths == null) card.videoPaths = new List<string>();
                                    EditorGUILayout.LabelField("视频列表");
                                    for (int n = 0; n < card.videoPaths.Count; n++)
                                    {
                                        EditorGUILayout.BeginHorizontal();
                                        VideoClip cardVideo = null;
                                        if (!string.IsNullOrEmpty(card.videoPaths[n]))
                                            cardVideo = Resources.Load<VideoClip>(card.videoPaths[n]);
                                        EditorGUI.BeginChangeCheck();
                                        cardVideo = (VideoClip)EditorGUILayout.ObjectField($"视频{n+1}", cardVideo, typeof(VideoClip), false);
                                        if (EditorGUI.EndChangeCheck() && cardVideo != null)
                                        {
                                            string assetPath = AssetDatabase.GetAssetPath(cardVideo);
                                            card.videoPaths[n] = GetResourcePath(assetPath);
                                        }
                                        if (GUILayout.Button("删除视频", GUILayout.Width(60)))
                                        {
                                            card.videoPaths.RemoveAt(n);
                                            n--;
                                        }
                                        EditorGUILayout.EndHorizontal();
                                    }
                                    if (GUILayout.Button("添加视频"))
                                    {
                                        card.videoPaths.Add("");
                                    }
                                    if (GUILayout.Button("删除卡牌", GUILayout.Width(80)))
                                    {
                                        item.cardItems.RemoveAt(k);
                                        k--;
                                    }
                                    EditorGUILayout.EndVertical();
                                }
                                if (GUILayout.Button("添加卡牌"))
                                {
                                    item.cardItems.Add(new CardItem());
                                }
                            }
                            // ====== 卡包相关UI结束 ======

                            // 删除商品按钮
                            EditorGUILayout.Space();
                            if (GUILayout.Button("删除商品", GUILayout.Width(80)))
                            {
                                if (EditorUtility.DisplayDialog("确认删除",
                                    $"确定要删除商品 '{item.name}' 吗？",
                                    "删除", "取消"))
                                {
                                    category.items.RemoveAt(i);
                                    SaveShopData();
                                    EditorGUILayout.EndVertical();
                                    break;
                                }
                            }
                        }

                        EditorGUILayout.EndVertical();
                    }
                }
                
                EditorGUI.indentLevel--;
            }
            
            EditorGUILayout.EndVertical();
        }

        EditorGUILayout.EndScrollView();

        // 保存按钮
        EditorGUILayout.Space(10);
        if (GUILayout.Button("保存所有更改"))
        {
            SaveShopData();
        }
    }

    private void AddCategory(string name)
    {
        var newCategory = new ShopCategory
        {
            id = System.Guid.NewGuid().ToString(),
            name = name,
            items = new List<ShopItem>()
        };
        
        shopData.categories.Add(newCategory);
        SaveShopData();
    }

    private void AddItem(ShopCategory category, string name, int price, string description, string iconPath)
    {
        var newItem = new ShopItem
        {
            id = System.Guid.NewGuid().ToString(),
            name = name,
            price = price,
            description = description,
            iconPath = iconPath,
            isOneTime = false,
            isPurchased = false,
            effects = new List<VariableEffect>()
        };
        
        category.items.Add(newItem);
        SaveShopData();
    }

    private void AddEffect(ShopItem item, string variableName, float value, VariableEffectOperation operation)
    {
        var newEffect = new VariableEffect
        {
            variableName = variableName,
            value = value.ToString(),
            operation = operation
        };
        
        item.effects.Add(newEffect);
        SaveShopData();
    }

    /// <summary>
    /// 将完整路径转换为相对于Resources文件夹的路径
    /// </summary>
    private string GetResourcePath(string fullPath)
    {
        if (string.IsNullOrEmpty(fullPath)) return "";
        
        // 移除所有支持的文件扩展名
        string[] supportedExtensions = new string[] {
            // 图片格式
            ".png", ".jpg", ".jpeg",
            // 视频格式
            ".mp4", ".mov", ".avi", ".m4v"
        };
        
        foreach (var ext in supportedExtensions)
        {
            fullPath = fullPath.Replace(ext, "");
        }
        
        // 查找Resources文件夹的位置
        int resourcesIndex = fullPath.IndexOf("Resources/");
        if (resourcesIndex != -1)
        {
            // 提取Resources文件夹之后的路径
            return fullPath.Substring(resourcesIndex + "Resources/".Length);
        }
        
        Debug.LogWarning("选择的文件不在Resources文件夹中，请将文件移动到Resources文件夹");
        return "";
    }
} 
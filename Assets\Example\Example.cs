//------------------------------------------------------------------------------
// Copyright (c) 2018-2023 Beijing Bytedance Technology Co., Ltd.
// All Right Reserved.
// Unauthorized copying of this file, via any medium is strictly prohibited.
// Proprietary and confidential.
//------------------------------------------------------------------------------

using System.Collections.Generic;
using System.Threading;
using ByteDance.Union;
using ByteDance.Union.Mediation;
using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using System;

/// <summary>
/// The example for the SDK.
/// </summary>
///
public sealed class Example : MonoBehaviour
{
    [SerializeField]
    public Text information;

    public NativeAd bannerAd;                    // 自渲染banner，仅支持csj。推荐使用ExpressBannerAd
    public ExpressBannerAd mExpressBannerAd;     // 模板banner，支持csj和融合
    public BUSplashAd splashAd;                  // 开屏广告，支持csj和融合
    public ExpressAd mExpressFeedad;             // 模板feed，仅支持csj
    public FeedAd feedAd;                        // 自渲染feed，支持csj和融合。在融合里模板和自渲染都支持。
    public DrawFeedAd drawFeedAd;                // drawFeed，仅支持融合
    public FullScreenVideoAd fullScreenVideoAd;  // 插全屏和新插屏，支持csj和融合
    public RewardVideoAd rewardAd;               // 激励视频，支持csj和融合

    // Unity 主线程ID:
    public static int MainThreadId;
    public static int MNowPlayAgainCount = 0;
    public static int MNextPlayAgainCount = 0;

    public static bool useMediation = true;

    private void Awake()
    {
        MainThreadId = Thread.CurrentThread.ManagedThreadId;
    }

    private void SdkInitCallback(bool success, string message)
    {
        // 注意：在初始化回调成功后再请求广告
        Debug.Log("CSJM_Unity "+"sdk初始化结束：success: " + success + ", message: " + message);
        // 也可以调用sdk的函数，判断sdk是否初始化完成
        Debug.Log("CSJM_Unity "+"sdk是否初始化成功, IsSdkReady: " + Pangle.IsSdkReady());
    }

    public bool testMode = true;

    void Start()
    {
        // 检查是否已经同意隐私政策
        if (PlayerPrefs.GetInt("PrivacyPolicyAgreed", 0) == 1||testMode)
        {
            InitializeSDK();
        }
        else
        {
            // 等待隐私政策同意事件
            PrivacyPolicyDialog.OnPrivacyAgreed += InitializeSDK;
        }
    }

    private void InitializeSDK()
    {
        // sdk初始化
        SDKConfiguration sdkConfiguration = new SDKConfiguration.Builder()
            .SetAppId(CSJMDAdPositionId.APP_ID)
            .SetAppName("APP测试媒体")
            .SetUseMediation(Example.useMediation) // 是否使用融合功能，置为false，可不初始化聚合广告相关模块
            .SetDebug(true) // debug日志开关，app发版时记得关闭
            .SetMediationConfig(GetMediationConfig())
            .SetPrivacyConfigurationn(GetPrivacyConfiguration())
            .SetAgeGroup(0)
            .SetPaid(false) // 是否是付费用户
            .SetTitleBarTheme(AdConst.TITLE_BAR_THEME_LIGHT) // 设置落地页主题
            .SetKeyWords("") // 设置用户画像关键词列表
            .Build();

        Pangle.Init(sdkConfiguration); // 合规要求，初始化分为2步，第一步先调用init
        Pangle.Start(SdkInitCallback); // 第二步再调用start。注意在初始化回调成功后再请求广告
    }

    private void OnDestroy()
    {
        // 移除事件监听
        PrivacyPolicyDialog.OnPrivacyAgreed -= InitializeSDK;
    }

    /* 💖💖💖💖��💖💖💖💖💖💖💖💖💖 ↓↓↓↓↓↓↓↓↓↓ 广告sdk初始化 及 其他设置相关 ↓↓↓↓↓↓↓↓↓↓ 💖💖💖💖💖💖💖💖💖💖💖💖💖💖 */

    /**
     * 初始化时进行隐私合规相关配置。不设置的将使用默认值
     */
    private PrivacyConfiguration GetPrivacyConfiguration()
    {
        // 这里仅展示了部分设置，开发者根据自己需要进行设置，不设置的将使用默认值，默认值可能不合规。
        PrivacyConfiguration privacyConfig = new PrivacyConfiguration();
        privacyConfig.CanUsePhoneState = false;
        privacyConfig.CanUseLocation = false;
        privacyConfig.Longitude = 115.7;
        privacyConfig.Latitude = 39.4;
        //privacyConfig.CustomIdfa = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx";


        // 融合相关配置示例
        privacyConfig.MediationPrivacyConfig = new MediationPrivacyConfig();
        privacyConfig.MediationPrivacyConfig.LimitPersonalAds = false;
        privacyConfig.MediationPrivacyConfig.ProgrammaticRecommend = false;
        privacyConfig.MediationPrivacyConfig.forbiddenCAID = false;
        privacyConfig.MediationPrivacyConfig.CanUseOaid = false;

        return privacyConfig;
    }

    /**
     * 使用融合功能时，初始化时进行相关配置
     */
    private MediationConfig GetMediationConfig()
    {
        MediationConfig mediationConfig = new MediationConfig();

        // 聚合配置json字符串（从gromore平台下载），用于首次安装时作为兜底配置使用。可选
        mediationConfig.CustomLocalConfig = MediationLocalConfig.CONFIG_JSON_STR;

        // 流量分组功能，可选
        MediationConfigUserInfoForSegment segment = new MediationConfigUserInfoForSegment();
        segment.Age = 18;
        segment.Gender = AdConst.GENDER_MALE;
        segment.Channel = "mediation-unity";
        segment.SubChannel = "mediation-sub-unity";
        segment.UserId = "mediation-userId-unity";
        segment.UserValueGroup = "mediation-user-value-unity";
        segment.CustomInfos = new Dictionary<string, string>
        {
            { "customKey", "customValue" }
        };
        mediationConfig.MediationConfigUserInfoForSegment = segment;

        return mediationConfig;
    }

    /* 💖💖💖💖💖💖💖💖💖💖💖💖💖💖 ↑↑↑↑↑↑↑↑↑↑ 广告sdk初始化 及 其他设置相关 ↑↑↑↑↑↑↑↑↑↑ 💖💖💖💖💖💖💖💖💖💖💖💖💖💖 */


    /* 💛💛💛💛💛💛💛💛💛💛💛💛💛💛 ↓↓↓↓↓↓↓↓↓↓ 激励视频相关样例 ↓↓↓↓↓↓↓↓↓↓ 💛💛💛💛💛💛💛💛💛💛💛💛💛💛 */

    // Load the reward Ad.
    public void LoadRewardAd()
    {
        ExampleRewardAd.LoadReward(this, false);
    }

    // Show the reward Ad.
    public void ShowRewardAd()
    {
        ExampleRewardAd.ShowReward(this);
    }

    // load mediation reward ad
    public void LoadMediationRewardAd()
    {
        ExampleRewardAd.LoadReward(this, true);
    }

    // Show the mediation reward Ad.
    public void ShowMediationRewardAd()
    {
        ExampleRewardAd.ShowReward(this);
    }
    /* 💛💛💛💛💛💛💛💛💛💛💛💛💛💛 ↑↑↑↑↑↑↑↑↑↑ 激励视频相关样例 ↑↑↑↑↑↑↑↑↑↑ 💛💛💛💛💛💛💛💛💛💛💛💛💛💛 */


    /* 💜💜💜💜💜💜💜💜💜💜💜💜💜💜 ↓↓↓↓↓↓↓↓↓↓ 开屏广告相关样例 ↓↓↓↓↓↓↓↓↓↓ 💜💜💜💜💜💜💜💜💜💜💜💜💜💜 */

    // 加载开屏广告
    public void LoadSplashAd()
    {
        if (splashAd != null)
        {
            splashAd.Dispose();
            splashAd = null;
        }

        int mSplashExpressWidthDp = 0;
        int mSplashExpressHeightDp = 0;
#if UNITY_ANDROID
        AndroidJavaClass unityClass = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
        AndroidJavaObject unityContext = unityClass.GetStatic<AndroidJavaObject>("currentActivity");
        float scale = unityContext.Call<AndroidJavaObject>("getResources").
            Call<AndroidJavaObject>("getDisplayMetrics").Get<float>("density");
        mSplashExpressWidthDp = (int)(Screen.width/scale + 0.5f);//根据设备像素宽度获取设备宽度DP
        mSplashExpressHeightDp = (int)(Screen.height/scale + 0.5f);//根据设备像素高度获取设备高度DP
#endif

        // 开屏自定义兜底，可选
        var mediationSplashReqInfo = new MediationSplashRequestInfo();
        mediationSplashReqInfo.AdnName = AdConst.ADN_PANGLE;
        mediationSplashReqInfo.AppId = CSJMDAdPositionId.M_SPLASH_BASELINE_APPID;
        mediationSplashReqInfo.Appkey = ""; // 穿山甲不需要appkey
        mediationSplashReqInfo.AdnSlotId = CSJMDAdPositionId.M_SPLASH_BASELINE_ID;

        string codeId = CSJMDAdPositionId.CSJ_SPLASH_V_ID;
        var adSlot = new AdSlot.Builder()
            .SetCodeId(codeId) // 必传
            .SetExpressViewAcceptedSize(mSplashExpressWidthDp, mSplashExpressHeightDp)  //普通开屏也需要设置模版size，单位dp
            .SetImageAcceptedSize(Screen.width, Screen.height) // 单位px
            .SetMediationAdSlot(new MediationAdSlot.Builder()
                .SetScenarioId("ScenarioId") // 可选
                .SetBidNotify(true) // 可选
                .SetMediationSplashRequestInfo(mediationSplashReqInfo) // 可选
                .Build())
            .Build();
        SDK.CreateAdNative().LoadSplashAd(adSlot, new SplashAdLoadListener(this), 3500);

        Debug.Log("[AdManager] 开始加载开屏广告");
    }

    // 显示开屏广告
    public void ShowSplashAd()
    {
        if (splashAd != null)
        {
            Debug.Log("[AdManager] 显示开屏广告");

            // 设置自定义监听器
            splashAd.SetSplashInteractionListener(new CustomSplashListener(this));
            splashAd.SetDownloadListener(new AppDownloadListener(this));
            splashAd.SetAdInteractionListener(new TTAdInteractionListener());

            // 显示广告
            splashAd.ShowSplashAd();
        }
        else
        {
            Debug.LogError("[AdManager] 开屏广告未加载，无法显示");
        }
    }

    // 加载并显示开屏广告
    public void LoadAndShowSplashAd()
    {
        // 在加载广告前先暂停游戏
        Time.timeScale = 0f;
        Debug.Log("[AdManager] Example.LoadAndShowSplashAd设置Time.timeScale = 0");

        ExampleSplashAd.LoadAndShowSplashAd(this, false);

        // 在加载广告后，等待一帧，然后添加我们自己的监听器
        StartCoroutine(AddCustomSplashListener());
    }

    // 协程用于等待广告加载完成后添加自定义监听器
    private IEnumerator AddCustomSplashListener()
    {
        // 等待几帧，确保广告已加载
        for (int i = 0; i < 5; i++)
        {
            yield return null;
        }

        // 检查广告是否已加载
        if (splashAd != null)
        {
            Debug.Log("[AdManager] 添加自定义开屏广告交互监听器");

            // 替换为我们自己的监听器
            try
            {
                // 使用自定义监听器
                splashAd.SetSplashInteractionListener(new CustomSplashListener(this));
            }
            catch (Exception ex)
            {
                Debug.LogError($"[AdManager] 设置自定义监听器失败: {ex.Message}");
            }

            // 在splashAd加载成功后
            if (splashAd != null && AdManager.Instance != null)
            {
                AdManager.Instance.OnSplashAdLoaded();
            }
        }
        else
        {
            Debug.LogWarning("[AdManager] splashAd为空，无法设置自定义监听器");
        }
    }

    // 自定义广告监听器类，直接在Example.cs中定义
    private class CustomSplashListener : ISplashAdInteractionListener
    {
        private Example example;

        public CustomSplashListener(Example example)
        {
            this.example = example;
            Debug.Log("[AdManager] 创建CustomSplashListener实例");
        }

        public void OnAdClicked(int type)
        {
            Debug.Log($"[AdManager] 开屏广告被点击: {type}");
        }

        public void OnAdDidShow(int type)
        {
            Debug.Log($"[AdManager] 开屏广告显示: {type}");
            // 通知AdManager广告已显示
            if (AdManager.Instance != null)
            {
                AdManager.Instance.NotifyAdShown();
            }

            // 直接在这里暂停游戏，确保广告显示时游戏暂停
            Time.timeScale = 0f;
            Debug.Log("[AdManager] 开屏广告显示时直接设置Time.timeScale = 0");
        }

        public void OnAdWillShow(int type)
        {
            Debug.Log($"[AdManager] 开屏广告即将显示: {type}");
        }

        public void OnAdClose(int type)
        {
            Debug.Log($"[AdManager] 开屏广告关闭: {type}");

            // 清理广告对象
            if (example.splashAd != null)
            {
                example.splashAd.Dispose();
                example.splashAd = null;
            }

            // 直接在这里恢复游戏，确保广告关闭时游戏恢复
            Time.timeScale = 1f;
            Debug.Log("[AdManager] 开屏广告关闭时直接设置Time.timeScale = 1");

            // 通知AdManager广告已关闭
            if (AdManager.Instance != null)
            {
                AdManager.Instance.NotifySplashAdClosed();
            }
        }
    }

    // 开屏广告加载监听器
    private class SplashAdLoadListener : ISplashAdListener
    {
        private Example example;

        public SplashAdLoadListener(Example example)
        {
            this.example = example;
        }

        public void OnSplashLoadFail(int code, string message)
        {
            Debug.LogError($"[AdManager] 开屏广告加载失败: {code}, {message}");

            if (example.splashAd != null)
            {
                example.splashAd.Dispose();
                example.splashAd = null;
            }

            // 通知AdManager广告加载失败
            if (AdManager.Instance != null)
            {
                AdManager.Instance.OnAdError($"开屏广告加载失败: {message}");
            }
        }

        public void OnSplashLoadSuccess(BUSplashAd ad)
        {
            Debug.Log("[AdManager] 开屏广告加载成功");
#if UNITY_IOS
            example.splashAd = ad;

            // 通知AdManager广告加载成功
            if (AdManager.Instance != null)
            {
                AdManager.Instance.OnSplashAdLoaded();
            }
#endif
        }

        public void OnSplashRenderSuccess(BUSplashAd ad)
        {
            Debug.Log("[AdManager] 开屏广告渲染成功");
#if UNITY_ANDROID
            example.splashAd = ad;

            // 通知AdManager广告加载成功
            if (AdManager.Instance != null)
            {
                AdManager.Instance.OnSplashAdLoaded();
            }
#endif
        }

        public void OnSplashRenderFail(int code, string message)
        {
            Debug.LogError($"[AdManager] 开屏广告渲染失败: {code}, {message}");

            // 通知AdManager广告加载失败
            if (AdManager.Instance != null)
            {
                AdManager.Instance.OnAdError($"开屏广告渲染失败: {message}");
            }
        }
    }

    // load and show mediation splash ad
    public void LoadAndShowMediationSplashAd()
    {
        ExampleSplashAd.LoadAndShowSplashAd(this, true);
    }

    /* 💜💜💜💜💜💜💜💜💜💜💜💜💜💜 ↑↑↑↑↑↑↑↑↑↑ 开屏广告相关样例 ↑↑↑↑↑↑↑↑↑↑ 💜💜💜💜💜💜💜💜💜💜💜💜💜💜 */


    /* ❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️ ↓↓↓↓↓↓↓↓↓↓ 插全屏广告相关样例 ↓↓↓↓↓↓↓↓↓↓ ❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️ */
    // Loads the full screen video ad.
    public void LoadFullScreenVideoAd()
    {
        ExampleFullScreenVideoAd.LoadFullScreenVideoAd(this, false);
    }

    // Show the fullScreen Ad.
    public void ShowFullScreenVideoAd()
    {
        ExampleFullScreenVideoAd.ShowFullScreenVideoAd(this);
    }

    // Loads the mediation full screen video ad.
    public void LoadMediationFullScreenVideoAd()
    {
        ExampleFullScreenVideoAd.LoadFullScreenVideoAd(this, true);
    }

    // Show the mediation full screen Ad.
    public void ShowMediationFullScreenVideoAd()
    {
        ExampleFullScreenVideoAd.ShowFullScreenVideoAd(this);
    }
    /* ❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️ ↑↑↑↑↑↑↑↑↑↑ 插全屏广告相关样例 ↑↑↑↑↑↑↑↑↑↑ ❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️ */


    /* 💙💙💙💙💙💙💙💙💙💙💙💙💙💙 ↓↓↓↓↓↓↓↓↓↓ banner广告相关样例 ↓↓↓↓↓↓↓↓↓↓ 💙💙💙💙💙💙💙💙💙💙💙💙💙💙 */
    public void LoadNativeBannerAd()
    {
        ExampleBannerAd.LoadNativeBannerAd(this);
    }

    public void ShowNativeBannerAd()
    {
        ExampleBannerAd.ShowNativeBannerAd(this);
    }

    // load express banner
    public void LoadExpressBannerAd()
    {
        ExampleExpressBannerAd.LoadExpressBannerAd(this, false);
    }

    // Show the express banner Ad.
    public void ShowExpressBannerAd()
    {
        ExampleExpressBannerAd.ShowExpressBannerAd(this);
    }

    // load mediation banner
    public void LoadMediationBannerAd()
    {
        ExampleExpressBannerAd.LoadExpressBannerAd(this, true);
    }

    // Show the mediation banner Ad.
    public void ShowMediationBannerAd()
    {
        ExampleExpressBannerAd.ShowExpressBannerAd(this);
    }
    /* 💙💙💙💙💙💙💙💙💙💙💙💙💙💙 ↑↑↑↑↑↑↑↑↑↑ banner广告相关样例 ↑↑↑↑↑↑↑↑↑↑ 💙💙💙💙💙💙💙💙💙💙💙💙💙💙 */


    /* 🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤 ↓↓↓↓↓↓↓↓↓↓ feed广告相关样例 ↓↓↓↓↓↓↓↓↓↓ 🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤 */
    // load express feed ad
    public void LoadExpressFeedAd()
    {
        ExampleExpressFeedAd.LoadExpressFeedAd(this);
    }

    // Show the expressFeed Ad.
    public void ShowExpressFeedAd()
    {
        ExampleExpressFeedAd.ShowExpressFeedAd(this);
    }

    // load feed ad.
    public void LoadFeedAd()
    {
        ExampleFeedAd.LoadFeedAd(this, false);
    }

    // Show the Feed Ad.
    public void ShowFeedAd()
    {
        ExampleFeedAd.ShowFeedAd(this);
    }

    // load mediation feed ad.
    public void LoadMediationFeedAd()
    {
        ExampleFeedAd.LoadFeedAd(this, true);
    }

    // Show the mediation Feed Ad.
    public void ShowMediationFeedAd()
    {
        ExampleFeedAd.ShowFeedAd(this);
    }
    /* 🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤 ↑↑↑↑↑↑↑↑↑↑ feed广告相关样例 ↑↑↑↑↑↑↑↑↑↑ 🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤 */


    /* 🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤 ↓↓↓↓↓↓↓↓↓↓ DrawFeed广告相关样例 ↓↓↓↓↓↓↓↓↓↓ 🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤 */

    // load mediation draw feed ad
    public void LoadMediationDrawFeedAd()
    {
        ExampleDrawFeedAd.LoadDrawFeedAd(this);
    }

    // show mediation draw feed ad
    public void ShowMediationDrawFeedAd()
    {
        ExampleDrawFeedAd.ShowDrawFeedAd(this);
    }

    /* 🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤 ↑↑↑↑↑↑↑↑↑↑ DrawFeed广告相关样例 ↑↑↑↑↑↑↑↑↑↑ 🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤🖤 */

    // Dispose the reward Ad.
    public void DisposeAds()
    {
        // 激励
        if (this.rewardAd != null)
        {
            this.rewardAd.Dispose();
            this.rewardAd = null;
        }

        // 全屏/新插屏
        if (this.fullScreenVideoAd != null)
        {
            this.fullScreenVideoAd.Dispose();
            this.fullScreenVideoAd = null;
        }

        // banner
        if (this.bannerAd != null)
        {
            this.bannerAd.Dispose();
            this.bannerAd = null;
        }
        if (this.mExpressBannerAd != null)
        {
            this.mExpressBannerAd.Dispose();
            this.mExpressBannerAd = null;
        }

        // 信息流
        if (this.feedAd != null)
        {
            this.feedAd.Dispose();
            this.feedAd = null;
        }
        if (this.mExpressFeedad != null)
        {
            this.mExpressFeedad.Dispose();
            this.mExpressFeedad = null;
        }
        if (this.drawFeedAd != null)
        {
            this.drawFeedAd.Dispose();
            this.drawFeedAd = null;
        }

        // 开屏
        if (this.splashAd != null)
        {
            this.splashAd.Dispose();
            this.splashAd = null;
        }
    }
}

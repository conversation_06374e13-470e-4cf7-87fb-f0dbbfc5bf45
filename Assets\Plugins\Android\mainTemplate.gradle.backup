apply plugin: 'com.android.library'
**APPLY_PLUGINS**

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
// Android Resolver Dependencies Start
    implementation 'com.android.support:appcompat-v7:28.0.0' // Assets/CSJ/Editor/PangleAdapterScriptsDependencies.xml:27
    implementation 'com.android.support:support-v4:28.0.0' // Assets/CSJ/Editor/PangleAdapterScriptsDependencies.xml:28
    implementation 'com.pangle.cn:mediation-baidu-adapter:9.37.0' // Assets/CSJ/Editor/PangleAdapterScriptsDependencies.xml:32
    implementation 'com.pangle.cn:mediation-gdt-adapter:4.591.1461.2' // Assets/CSJ/Editor/PangleAdapterScriptsDependencies.xml:30
    implementation 'com.pangle.cn:mediation-klevin-adapter:********.23' // Assets/CSJ/Editor/PangleAdapterScriptsDependencies.xml:36
    implementation 'com.pangle.cn:mediation-ks-adapter:********.0' // Assets/CSJ/Editor/PangleAdapterScriptsDependencies.xml:31
    implementation 'com.pangle.cn:mediation-mintegral-adapter:*********' // Assets/CSJ/Editor/PangleAdapterScriptsDependencies.xml:33
    implementation 'com.pangle.cn:mediation-sigmob-adapter:********' // Assets/CSJ/Editor/PangleAdapterScriptsDependencies.xml:35
    implementation 'com.pangle.cn:mediation-unity-adapter:********' // Assets/CSJ/Editor/PangleAdapterScriptsDependencies.xml:34
// Android Resolver Dependencies End
**DEPS**}

// Android Resolver Exclusions Start
android {
  packagingOptions {
      exclude ('/lib/arm64-v8a/*' + '*')
      exclude ('/lib/armeabi/*' + '*')
      exclude ('/lib/mips/*' + '*')
      exclude ('/lib/mips64/*' + '*')
      exclude ('/lib/x86/*' + '*')
      exclude ('/lib/x86_64/*' + '*')
  }
}
// Android Resolver Exclusions End
android {
    ndkPath "**NDKPATH**"

    compileSdkVersion **APIVERSION**
    buildToolsVersion '**BUILDTOOLS**'

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    defaultConfig {
        minSdkVersion **MINSDKVERSION**
        targetSdkVersion **TARGETSDKVERSION**
        ndk {
            abiFilters **ABIFILTERS**
        }
        versionCode **VERSIONCODE**
        versionName '**VERSIONNAME**'
        consumerProguardFiles 'proguard-unity.txt'**USER_PROGUARD**
    }

    lintOptions {
        abortOnError false
    }

    aaptOptions {
        noCompress = **BUILTIN_NOCOMPRESS** + unityStreamingAssets.tokenize(', ')
        ignoreAssetsPattern = "!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"
    }**PACKAGING_OPTIONS**
}
**IL_CPP_BUILD_SETUP**
**SOURCE_BUILD_SETUP**
**EXTERNAL_SOURCES**

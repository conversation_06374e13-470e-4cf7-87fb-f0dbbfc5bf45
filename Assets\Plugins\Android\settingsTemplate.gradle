pluginManagement {
    repositories {
        **ARTIFACTORYREPOSITORY**
        gradlePluginPortal()
        google()
        mavenCentral()
    }
}

include ':launcher', ':unityLibrary'
**INCLUDES**

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_SETTINGS)
    repositories {
        **ARTIFACTORYREPOSITORY**
        google()
        mavenCentral()
// Android Resolver Repos Start
        def unityProjectPath = $/file:///**DIR_UNITYPROJECT**/$.replace("\\", "/")
        maven {
            url "https://artifact.bytedance.com/repository/pangle" // Assets/CSJ/Editor/PangleAdapterScriptsDependencies.xml:14
        }
        maven {
            url "https://maven.aliyun.com/repository/jcenter" // Assets/CSJ/Editor/PangleAdapterScriptsDependencies.xml:14
        }
        maven {
            url "https://maven.aliyun.com/repository/google" // Assets/CSJ/Editor/PangleAdapterScriptsDependencies.xml:14
        }
        mavenLocal()
// Android Resolver Repos End
        flatDir {
            dirs "${project(':unityLibrary').projectDir}/libs"
        }
    }
}
